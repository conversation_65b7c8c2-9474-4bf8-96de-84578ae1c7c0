{"name": "vibrasonix-music-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 npx expo start", "build:web": "npx expo export --platform web", "lint": "npx expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-community/slider": "^4.5.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "expo": "53.0.12", "expo-av": "~15.1.6", "expo-blur": "~14.1.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "firebase": "^10.8.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.15.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.19.10", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.3", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "typescript": "~5.8.3"}}