import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { supabase, DatabaseService } from '@/lib/supabase';
import { DodoPaymentService } from '@/lib/payments';
import type { User as SupabaseUser } from '@supabase/supabase-js';
import type { User } from '@/lib/supabase';
import { Alert, Platform } from 'react-native';

interface AuthContextType {
  user: User | null;
  supabaseUser: SupabaseUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  refreshPaymentStatus: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  resendConfirmation: (email: string) => Promise<void>;
  isPremium: boolean;
  emailConfirmationRequired: boolean;
  setEmailConfirmationRequired: (required: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [emailConfirmationRequired, setEmailConfirmationRequired] = useState(false);

  // Track if initial session check is complete to prevent race conditions
  const initialSessionChecked = useRef(false);
  const isProcessingAuth = useRef(false);
  const authInitialized = useRef(false);

  useEffect(() => {
    // Prevent multiple initializations
    if (authInitialized.current) {
      console.log('🔒 Auth already initialized, skipping...');
      return;
    }

    authInitialized.current = true;
    let isMounted = true;
    let authTimeout: ReturnType<typeof setTimeout>;

    const initializeAuth = async () => {
      try {
        console.log('🔑 Initializing auth...');

        // Set a timeout to prevent infinite loading
        authTimeout = setTimeout(() => {
          if (isMounted && loading) {
            console.log('⏰ Auth initialization timeout, setting loading to false');
            setLoading(false);
          }
        }, 10000); // 10 second timeout

        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting initial session:', error);
          if (isMounted) {
            setLoading(false);
          }
          return;
        }

        console.log('📋 Initial session check:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email,
          emailConfirmed: !!session?.user?.email_confirmed_at
        });

        if (session?.user && isMounted) {
          // Check email confirmation
          if (!session.user.email_confirmed_at) {
            console.log('📧 Email confirmation required');
            setEmailConfirmationRequired(true);
            setSupabaseUser(session.user);
            setLoading(false);
            return;
          }

          // User is authenticated and email is confirmed
          console.log('✅ User authenticated, loading profile...');
          setEmailConfirmationRequired(false);
          setSupabaseUser(session.user);
          await loadUserProfile(session.user.id, session.user);
        } else if (isMounted) {
          // No session
          console.log('❌ No session found');
          setLoading(false);
        }

        initialSessionChecked.current = true;
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Initialize auth
    initializeAuth();

    // Listen for auth changes - simplified to only handle essential events
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state changed:', event, {
        hasSession: !!session,
        userEmail: session?.user?.email,
        emailConfirmed: !!session?.user?.email_confirmed_at
      });

      // Skip INITIAL_SESSION since we handle it in initializeAuth
      if (event === 'INITIAL_SESSION') {
        console.log('⏭️ Skipping INITIAL_SESSION (handled in initializeAuth)');
        return;
      }

      if (!isMounted) return;

      try {
        if (event === 'SIGNED_IN') {
          console.log('✅ User signed in');

          if (session?.user) {
            if (!session.user.email_confirmed_at) {
              setEmailConfirmationRequired(true);
              setSupabaseUser(session.user);
              setLoading(false);
              showAlert('Email confirmation required', 'Please check your email and click the confirmation link to access your account.');
            } else {
              setEmailConfirmationRequired(false);
              setSupabaseUser(session.user);
              await loadUserProfile(session.user.id, session.user);
            }
          }
        } else if (event === 'SIGNED_OUT') {
          console.log('❌ User signed out');
          setSupabaseUser(null);
          setUser(null);
          setEmailConfirmationRequired(false);
          setLoading(false);
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          console.log('🔄 Token refreshed');
          // Only update if user data changed
          if (session.user.email_confirmed_at && emailConfirmationRequired) {
            setEmailConfirmationRequired(false);
            showAlert('Email confirmed!', 'Your email has been confirmed. Welcome to the app!');
          }
          setSupabaseUser(session.user);
        }
      } catch (error) {
        console.error('Error processing auth state change:', error);
        setLoading(false);
      }
    });

    return () => {
      isMounted = false;
      if (authTimeout) {
        clearTimeout(authTimeout);
      }
      subscription.unsubscribe();
    };
  }, []); // Remove emailConfirmationRequired from dependencies to prevent loops

  const showAlert = (title: string, message: string) => {
    if (Platform.OS === 'web') {
      alert(`${title}: ${message}`);
    } else {
      Alert.alert(title, message);
    }
  };

  const loadUserProfile = async (userId: string, sessionUser?: SupabaseUser) => {
    try {
      console.log('👤 Loading user profile for:', userId);

      const userProfile = await DatabaseService.getUser(userId);

      if (userProfile === null) {
        console.log('👤 User profile not found, creating new profile...');

        // Use the passed sessionUser or the current supabaseUser
        const userToCreate = sessionUser || supabaseUser;

        if (userToCreate && userToCreate.email) {
          try {
            console.log('👤 Creating user with data:', {
              id: userId,
              email: userToCreate.email,
              display_name: userToCreate.user_metadata?.display_name || userToCreate.email.split('@')[0],
              authUid: userToCreate.id
            });

            // Check if the userId matches the auth user ID
            if (userId !== userToCreate.id) {
              console.error('❌ User ID mismatch:', { userId, authId: userToCreate.id });
              setUser(null);
              return;
            }

            const newUser = await DatabaseService.createUser({
              id: userId,
              email: userToCreate.email,
              display_name: userToCreate.user_metadata?.display_name || userToCreate.email.split('@')[0],
              avatar_url: userToCreate.user_metadata?.avatar_url,
              subscription_status: 'free'
            });

            console.log('✅ User profile created successfully:', newUser);
            setUser(newUser);
          } catch (createError: any) {
            console.error('❌ Error creating user profile:', createError);
            console.error('❌ Create error details:', {
              message: createError.message,
              code: createError.code,
              details: createError.details,
              hint: createError.hint
            });

            // If user already exists, try to fetch it
            if (createError.code === '23505' || createError.message?.includes('duplicate')) {
              console.log('👤 User already exists, trying to fetch...');
              try {
                const existingUser = await DatabaseService.getUser(userId);
                if (existingUser) {
                  console.log('✅ Found existing user profile');
                  setUser(existingUser);
                } else {
                  setUser(null);
                }
              } catch (fetchError) {
                console.error('❌ Error fetching existing user:', fetchError);
                setUser(null);
              }
            } else {
              setUser(null);
            }
          }
        } else {
          console.error('❌ No user data available for profile creation');
          setUser(null);
        }
      } else {
        console.log('✅ User profile loaded successfully:', userProfile);
        setUser(userProfile);
      }
    } catch (error) {
      console.error('❌ Error loading user profile:', error);
      console.error('❌ Load error details:', JSON.stringify(error, null, 2));
      setUser(null);
    } finally {
      console.log('🏁 Setting loading to false');
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        if (error.message.includes('Email not confirmed')) {
          setEmailConfirmationRequired(true);
          showAlert('Email confirmation required', 'Please check your email and click the confirmation link to access your account.');
        } else if (error.message.includes('Invalid login credentials')) {
          showAlert('Sign in failed', 'Invalid email or password. Please check your credentials and try again.');
        } else {
          showAlert('Sign in failed', error.message);
        }
        throw error;
      }

      // Check if email is confirmed
      if (data.user && !data.user.email_confirmed_at) {
        setEmailConfirmationRequired(true);
        showAlert('Email confirmation required', 'Please check your email and click the confirmation link to access your account.');
      }
    } catch (error: any) {
      setLoading(false);
      throw error;
    }
  };

  const signUp = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (error) {
        if (error.message.includes('User already registered')) {
          showAlert('Account exists', 'An account with this email already exists. Please sign in instead.');
        } else {
          showAlert('Sign up failed', error.message);
        }
        throw error;
      }

      if (data.user && !data.user.email_confirmed_at) {
        setEmailConfirmationRequired(true);
        showAlert('Check your email', 'Please check your email and click the confirmation link to complete your registration.');
      }
    } catch (error: any) {
      setLoading(false);
      throw error;
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      console.error('Error signing out:', error);
      showAlert('Sign out failed', 'There was an error signing out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      
      if (error) {
        showAlert('Password reset failed', error.message);
        throw error;
      }
      
      showAlert('Password reset sent', 'Please check your email for password reset instructions.');
    } catch (error: any) {
      throw error;
    }
  };

  const resendConfirmation = async (email: string) => {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });
      
      if (error) {
        showAlert('Resend failed', error.message);
        throw error;
      }
      
      showAlert('Confirmation email sent', 'Please check your email for the confirmation link.');
    } catch (error: any) {
      throw error;
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) throw new Error('No user logged in');
    
    try {
      const updatedUser = await DatabaseService.updateUser(user.id, updates);
      setUser(updatedUser);
    } catch (error) {
      console.error('Error updating profile:', error);
      showAlert('Update failed', 'There was an error updating your profile. Please try again.');
      throw error;
    }
  };

  const refreshPaymentStatus = async () => {
    if (!user) return;
    
    try {
      const paymentStatus = await DodoPaymentService.getPaymentStatus(user.id);
      if (paymentStatus.isPremium && user.subscription_status !== 'premium') {
        // Update local user state if payment status changed
        const updatedUser = { ...user, subscription_status: 'premium' as const };
        setUser(updatedUser);
      }
    } catch (error) {
      console.error('Error refreshing payment status:', error);
    }
  };

  const value = {
    user,
    supabaseUser,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    refreshPaymentStatus,
    resetPassword,
    resendConfirmation,
    isPremium: user?.subscription_status === 'premium',
    emailConfirmationRequired,
    setEmailConfirmationRequired,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};