import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';
import { Alert, Platform } from 'react-native';

interface AuthContextType {
  user: SupabaseUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  resendConfirmation: (email: string) => Promise<void>;
  emailConfirmationRequired: boolean;
  setEmailConfirmationRequired: (required: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Global state to prevent multiple auth initializations
let globalAuthState = {
  initialized: false,
  user: null as SupabaseUser | null,
  loading: true,
  emailConfirmationRequired: false
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(globalAuthState.user);
  const [loading, setLoading] = useState(globalAuthState.loading);
  const [emailConfirmationRequired, setEmailConfirmationRequired] = useState(globalAuthState.emailConfirmationRequired);

  useEffect(() => {
    // If already initialized, use global state
    if (globalAuthState.initialized) {
      console.log('🔒 Auth already initialized, using cached state');
      setUser(globalAuthState.user);
      setLoading(globalAuthState.loading);
      setEmailConfirmationRequired(globalAuthState.emailConfirmationRequired);
      return;
    }

    let isMounted = true;

    const initializeAuth = async () => {
      try {
        console.log('🔑 Checking auth session...');

        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          if (isMounted) {
            setLoading(false);
            globalAuthState.loading = false;
          }
          return;
        }

        console.log('📋 Session check result:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userEmail: session?.user?.email,
          emailConfirmed: !!session?.user?.email_confirmed_at
        });

        if (session?.user && isMounted) {
          // Update global state first
          globalAuthState.user = session.user;
          globalAuthState.emailConfirmationRequired = !session.user.email_confirmed_at;
          
          // Then update component state
          setUser(session.user);
          setEmailConfirmationRequired(!session.user.email_confirmed_at);
          
          if (!session.user.email_confirmed_at) {
            console.log('📧 Email confirmation required');
          } else {
            console.log('✅ User authenticated');
          }
        } else if (isMounted) {
          console.log('❌ No session found');
          globalAuthState.user = null;
          globalAuthState.emailConfirmationRequired = false;
          setUser(null);
          setEmailConfirmationRequired(false);
        }

        if (isMounted) {
          globalAuthState.loading = false;
          globalAuthState.initialized = true;
          setLoading(false);
        }
      } catch (error) {
        console.error('Error in auth initialization:', error);
        if (isMounted) setLoading(false);
      }
    };

    // Initialize
    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth event:', event);

      if (!isMounted) return;

      try {
        if (event === 'SIGNED_IN' && session?.user) {
          if (!session.user.email_confirmed_at) {
            setEmailConfirmationRequired(true);
            setUser(session.user);
            showAlert('Email confirmation required', 'Please check your email and click the confirmation link.');
          } else {
            setEmailConfirmationRequired(false);
            setUser(session.user);
          }
          setLoading(false);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setEmailConfirmationRequired(false);
          setLoading(false);
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          if (session.user.email_confirmed_at && emailConfirmationRequired) {
            setEmailConfirmationRequired(false);
            showAlert('Email confirmed!', 'Welcome to the app!');
          }
          setUser(session.user);
        }
      } catch (error) {
        console.error('Error in auth state change:', error);
        if (isMounted) setLoading(false);
      }
    });

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, [emailConfirmationRequired]); // Include emailConfirmationRequired in deps

  const showAlert = (title: string, message: string) => {
    if (Platform.OS === 'web') {
      alert(`${title}: ${message}`);
    } else {
      Alert.alert(title, message);
    }
  };

  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        if (error.message.includes('Email not confirmed')) {
          setEmailConfirmationRequired(true);
          showAlert('Email confirmation required', 'Please check your email and click the confirmation link to access your account.');
        } else if (error.message.includes('Invalid login credentials')) {
          showAlert('Sign in failed', 'Invalid email or password. Please check your credentials and try again.');
        } else {
          showAlert('Sign in failed', error.message);
        }
        throw error;
      }

      // Check if email is confirmed
      if (data.user && !data.user.email_confirmed_at) {
        setEmailConfirmationRequired(true);
        showAlert('Email confirmation required', 'Please check your email and click the confirmation link to access your account.');
      }
    } catch (error: any) {
      setLoading(false);
      throw error;
    }
  };

  const signUp = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (error) {
        if (error.message.includes('User already registered')) {
          showAlert('Account exists', 'An account with this email already exists. Please sign in instead.');
        } else {
          showAlert('Sign up failed', error.message);
        }
        throw error;
      }

      if (data.user && !data.user.email_confirmed_at) {
        setEmailConfirmationRequired(true);
        showAlert('Check your email', 'Please check your email and click the confirmation link to complete your registration.');
      }
    } catch (error: any) {
      setLoading(false);
      throw error;
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      console.error('Error signing out:', error);
      showAlert('Sign out failed', 'There was an error signing out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      
      if (error) {
        showAlert('Password reset failed', error.message);
        throw error;
      }
      
      showAlert('Password reset sent', 'Please check your email for password reset instructions.');
    } catch (error: any) {
      throw error;
    }
  };

  const resendConfirmation = async (email: string) => {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });
      
      if (error) {
        showAlert('Resend failed', error.message);
        throw error;
      }
      
      showAlert('Confirmation email sent', 'Please check your email for the confirmation link.');
    } catch (error: any) {
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    resendConfirmation,
    emailConfirmationRequired,
    setEmailConfirmationRequired,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};