import { supabase } from "@/lib/supabase";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

export async function fetchTracks() {
  const { data, error } = await supabase.from('tracks').select('*');
  if (error) throw error;
  return data;
}

export function useTracks() {
  return useQuery({
    queryKey: ['tracks'],
    queryFn: fetchTracks,
  });
}

export async function fetchTrackAudioUrl(trackId: string) {
  const { data, error } = await supabase.storage.from('tracks').createSignedUrl(trackId, 60 * 60 * 24);
  if (error) throw error;
  return data.signedUrl;
}

export function useTrackAudioUrl(trackId: string) {
  return useQuery({
    queryKey: ['trackAudioUrl', trackId],
    queryFn: () => fetchTrackAudioUrl(trackId),
  });
}