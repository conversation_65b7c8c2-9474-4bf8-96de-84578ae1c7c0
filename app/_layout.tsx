import { useEffect, useState, useRef } from 'react';
import { router, Stack, useRootNavigationState, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { MusicProvider } from '@/contexts/MusicContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import './global.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, ActivityIndicator } from 'react-native';

// Loading component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#0A0A0F'
    }}>
      <ActivityIndicator size="large" color="#A68752" />
    </View>
  );
}

// Navigation component that handles auth routing
function NavigationHandler({ children }: { children: React.ReactNode }) {
  const { user, loading, emailConfirmationRequired } = useAuth();
  const [isReady, setIsReady] = useState(false);
  const rootNavigationState = useRootNavigationState();
  const segments = useSegments();

  // Track navigation state
  const hasNavigated = useRef(false);
  const lastAuthState = useRef<string>('');
  const lastNavigatedRoute = useRef<string>('');

  useEffect(() => {
    // Wait for navigation system to be ready
    if (!rootNavigationState?.key) {
      console.log('⏳ Navigation system not ready');
      return;
    }

    // Wait for auth to finish loading
    if (loading) {
      console.log('⏳ Auth still loading');
      return;
    }

    // Always set ready to true once auth and navigation are ready
    if (!isReady) {
      console.log('✅ Auth and navigation ready, setting isReady to true');
      setIsReady(true);
    }

    // Create auth state signature
    const authState = `${!!user}-${emailConfirmationRequired}-${user?.email || 'none'}`;
    const currentRoute = segments[0];

    console.log('🔍 Navigation check:', {
      currentRoute,
      hasUser: !!user,
      userEmail: user?.email,
      emailConfirmed: !!user?.email_confirmed_at,
      emailConfirmationRequired,
      loading,
      hasNavigated: hasNavigated.current,
      authStateChanged: lastAuthState.current !== authState
    });

    // Determine target route
    let targetRoute: string;
    if (emailConfirmationRequired) {
      targetRoute = '(auth)';
    } else if (user && user.email) {
      targetRoute = '(tabs)';
    } else {
      targetRoute = '(auth)';
    }

    // Check if we need to navigate - fix route comparison
    const isOnCorrectRoute = currentRoute === targetRoute;
    const authStateChanged = lastAuthState.current !== authState;

    // More precise navigation logic
    const shouldNavigate =
      !hasNavigated.current || // Initial navigation
      (authStateChanged && !isOnCorrectRoute); // Auth changed AND we're not on correct route

    // Additional check: don't navigate to the same route we just navigated to
    const alreadyNavigatedToThisRoute = lastNavigatedRoute.current === targetRoute;

    console.log('🤔 Navigation decision:', {
      shouldNavigate,
      hasNavigated: hasNavigated.current,
      authStateChanged,
      isOnCorrectRoute,
      alreadyNavigatedToThisRoute,
      currentRoute,
      targetRoute,
      lastNavigatedRoute: lastNavigatedRoute.current,
      lastAuthState: lastAuthState.current,
      currentAuthState: authState
    });

    if (!shouldNavigate || alreadyNavigatedToThisRoute) {
      console.log('⏭️ Skipping navigation:', {
        reason: alreadyNavigatedToThisRoute ? 'already navigated to this route' :
                isOnCorrectRoute ? 'already on correct route' :
                !authStateChanged ? 'auth state unchanged' : 'other',
        currentRoute,
        targetRoute
      });

      return;
    }

    console.log('🧭 Navigating to:', targetRoute, {
      reason: !hasNavigated.current ? 'initial navigation' : 'auth changed',
      from: currentRoute,
      to: targetRoute
    });

    // Update state
    lastAuthState.current = authState;
    lastNavigatedRoute.current = targetRoute;
    hasNavigated.current = true;

    // Navigate
    router.replace(`/${targetRoute}` as any);

  }, [user, loading, emailConfirmationRequired, rootNavigationState?.key, segments, isReady]);

  // Show loading until ready
  if (!isReady) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}

export default function RootLayout() {
  useFrameworkReady();
  const [isReady, setIsReady] = useState(false);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
        retry: 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
      },
    },
  });

  useEffect(() => {
    // Small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationHandler>
          <MusicProvider>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="player" options={{ headerShown: false }} />
              <Stack.Screen name="album/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="light" />
          </MusicProvider>
        </NavigationHandler>
      </AuthProvider>
    </QueryClientProvider>
  );
}