import { useEffect, useState } from 'react';
import { router, Stack, useRootNavigationState } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { MusicProvider } from '@/contexts/MusicContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import './global.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, ActivityIndicator } from 'react-native';

// Loading component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#0A0A0F'
    }}>
      <ActivityIndicator size="large" color="#A68752" />
    </View>
  );
}

// Navigation component that handles auth routing
function NavigationHandler({ children }: { children: React.ReactNode }) {
  const { user, loading, emailConfirmationRequired } = useAuth();
  const rootNavigationState = useRootNavigationState();
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    // Wait for navigation system to be ready
    if (!rootNavigationState?.key) {
      console.log('⏳ Navigation system not ready');
      return;
    }

    // Prevent multiple navigations in progress
    if (isNavigating) {
      console.log('⏳ Navigation already in progress');
      return;
    }

    console.log('🚀 Checking navigation state:', {
      hasUser: !!user,
      userEmail: user?.email,
      emailConfirmed: !!user?.email_confirmed_at,
      emailConfirmationRequired,
      loading
    });

    // Handle navigation
    const navigate = async () => {
      setIsNavigating(true);
      try {
        if (emailConfirmationRequired) {
          console.log('📧 Email confirmation required → (auth)');
          await router.replace('/(auth)');
        } else if (user) {
          console.log('✅ User authenticated → (tabs)');
          await router.replace('/(tabs)');
        } else {
          console.log('❌ No authenticated user → (auth)');
          await router.replace('/(auth)');
        }
      } catch (error) {
        console.error('Navigation error:', error);
      } finally {
        setIsNavigating(false);
      }
    };

    // Only navigate if not loading
    if (!loading) {
      navigate();
    }

  }, [user, loading, emailConfirmationRequired, rootNavigationState?.key]);

  // Show loading only while auth is loading
  if (loading) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}

export default function RootLayout() {
  useFrameworkReady();
  const [isReady, setIsReady] = useState(false);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
        retry: 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
      },
    },
  });

  useEffect(() => {
    // Small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationHandler>
          <MusicProvider>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="player" options={{ headerShown: false }} />
              <Stack.Screen name="album/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="light" />
          </MusicProvider>
        </NavigationHandler>
      </AuthProvider>
    </QueryClientProvider>
  );
}