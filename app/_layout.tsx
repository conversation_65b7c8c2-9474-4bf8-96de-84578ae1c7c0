import { useEffect, useState } from 'react';
import { router, Stack, useRootNavigationState } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { MusicProvider } from '@/contexts/MusicContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import './global.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, ActivityIndicator } from 'react-native';

// Loading component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#0A0A0F'
    }}>
      <ActivityIndicator size="large" color="#A68752" />
    </View>
  );
}

// Navigation component that handles auth routing
function NavigationHandler({ children }: { children: React.ReactNode }) {
  const { user, loading, emailConfirmationRequired } = useAuth();
  const [initialNavigationDone, setInitialNavigationDone] = useState(false);
  const [lastNavigationTime, setLastNavigationTime] = useState(0);
  const rootNavigationState = useRootNavigationState();

  // Helper function to navigate with throttling
  const navigateWithThrottle = (path: string, reason: string) => {
    const now = Date.now();
    if (now - lastNavigationTime < 1000) { // Prevent navigation spam
      console.log(`⏳ Navigation throttled: ${reason}`);
      return;
    }

    console.log(`🧭 Navigating to ${path}: ${reason}`);
    setLastNavigationTime(now);
    router.replace(path as any);
  };

  useEffect(() => {
    // Wait for navigation to be ready and auth to finish loading
    if (!rootNavigationState?.key || loading) {
      return;
    }

    // Only do initial navigation once
    if (initialNavigationDone) {
      return;
    }

    console.log('🚀 Initial navigation check:', {
      user: !!user,
      userEmail: user?.email,
      loading,
      emailConfirmationRequired,
      initialNavigationDone
    });

    // Small delay to ensure auth state is stable
    const timer = setTimeout(() => {
      // Handle navigation based on auth state
      if (emailConfirmationRequired) {
        navigateWithThrottle('/(auth)', 'Email confirmation required');
      } else if (user && user.email) {
        navigateWithThrottle('/(tabs)', 'User authenticated');
      } else {
        navigateWithThrottle('/(auth)', 'No user found');
      }

      setInitialNavigationDone(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [user, loading, emailConfirmationRequired, rootNavigationState?.key, initialNavigationDone]);

  // Listen for auth state changes after initial navigation
  useEffect(() => {
    if (!initialNavigationDone || loading) {
      return;
    }

    console.log('🔄 Auth state changed:', {
      user: !!user,
      emailConfirmationRequired
    });

    // Handle auth state changes
    if (emailConfirmationRequired) {
      console.log('📧 Redirecting to auth for email confirmation');
      router.replace('/(auth)');
    } else if (user && user.email) {
      console.log('✅ User authenticated, redirecting to tabs');
      router.replace('/(tabs)');
    } else if (!user) {
      console.log('❌ User signed out, redirecting to auth');
      router.replace('/(auth)');
    }
  }, [user, emailConfirmationRequired, initialNavigationDone, loading]);

  // Show loading screen while auth is loading or initial navigation hasn't happened
  if (loading || !initialNavigationDone) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}

export default function RootLayout() {
  useFrameworkReady();
  const [isReady, setIsReady] = useState(false);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
        retry: 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
      },
    },
  });

  useEffect(() => {
    // Small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationHandler>
          <MusicProvider>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="player" options={{ headerShown: false }} />
              <Stack.Screen name="album/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="light" />
          </MusicProvider>
        </NavigationHandler>
      </AuthProvider>
    </QueryClientProvider>
  );
}