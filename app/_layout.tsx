import { useEffect, useState, useRef } from 'react';
import { router, Stack, useRootNavigationState } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { MusicProvider } from '@/contexts/MusicContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import './global.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, ActivityIndicator } from 'react-native';

// Loading component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#0A0A0F'
    }}>
      <ActivityIndicator size="large" color="#A68752" />
    </View>
  );
}

// Navigation component that handles auth routing
function NavigationHandler({ children }: { children: React.ReactNode }) {
  const { user, loading, emailConfirmationRequired } = useAuth();
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const rootNavigationState = useRootNavigationState();
  const hasNavigatedRef = useRef(false);

  useEffect(() => {
    // Wait for navigation to be ready and auth to finish loading
    if (!rootNavigationState?.key || loading) {
      console.log('⏳ Waiting for navigation/auth:', {
        navigationReady: !!rootNavigationState?.key,
        loading
      });
      return;
    }

    // Prevent multiple navigations
    if (hasNavigatedRef.current) {
      console.log('✅ Navigation already completed, skipping');
      return;
    }

    console.log('🚀 Navigation check:', {
      user: !!user,
      userEmail: user?.email,
      loading,
      emailConfirmationRequired
    });

    // Determine where to navigate
    let targetRoute = '/(auth)'; // default
    let reason = 'No user found';

    if (emailConfirmationRequired) {
      targetRoute = '/(auth)';
      reason = 'Email confirmation required';
    } else if (user && user.email) {
      targetRoute = '/(tabs)';
      reason = 'User authenticated';
    }

    console.log(`🧭 Navigating to ${targetRoute}: ${reason}`);

    // Mark as navigated and navigate
    hasNavigatedRef.current = true;
    router.replace(targetRoute as any);
    setIsNavigationReady(true);

  }, [user, loading, emailConfirmationRequired, rootNavigationState?.key]);

  // Show loading screen while not ready
  if (loading || !isNavigationReady) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}

export default function RootLayout() {
  useFrameworkReady();
  const [isReady, setIsReady] = useState(false);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
        retry: 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
      },
    },
  });

  useEffect(() => {
    // Small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationHandler>
          <MusicProvider>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="player" options={{ headerShown: false }} />
              <Stack.Screen name="album/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="light" />
          </MusicProvider>
        </NavigationHandler>
      </AuthProvider>
    </QueryClientProvider>
  );
}