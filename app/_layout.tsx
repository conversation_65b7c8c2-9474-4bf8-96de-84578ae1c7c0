import { useEffect, useState, useRef } from 'react';
import { router, Stack, useRootNavigationState, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { MusicProvider } from '@/contexts/MusicContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import './global.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, ActivityIndicator } from 'react-native';

// Loading component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#0A0A0F'
    }}>
      <ActivityIndicator size="large" color="#A68752" />
    </View>
  );
}

// Navigation component that handles auth routing
function NavigationHandler({ children }: { children: React.ReactNode }) {
  const { user, loading, emailConfirmationRequired } = useAuth();
  const [isNavigationComplete, setIsNavigationComplete] = useState(false);
  const rootNavigationState = useRootNavigationState();
  const segments = useSegments();

  // Track if we've done the initial navigation
  const initialNavigationDone = useRef(false);

  // Track the last auth state to prevent unnecessary navigations
  const lastAuthState = useRef<string>('');

  useEffect(() => {
    // Wait for navigation system to be ready
    if (!rootNavigationState?.key) {
      console.log('⏳ Navigation system not ready');
      return;
    }

    // Wait for auth to finish loading
    if (loading) {
      console.log('⏳ Auth still loading');
      return;
    }

    // Create a unique auth state string for comparison
    const currentAuthState = `${!!user}-${emailConfirmationRequired}-${user?.email || 'none'}`;

    // Get current route
    const currentRoute = segments[0];

    console.log('🔍 Navigation check:', {
      currentRoute,
      user: !!user,
      userEmail: user?.email,
      emailConfirmationRequired,
      loading,
      initialNavigationDone: initialNavigationDone.current,
      authStateChanged: lastAuthState.current !== currentAuthState
    });

    // Determine target route
    let targetRoute: string;
    if (emailConfirmationRequired) {
      targetRoute = '(auth)';
    } else if (user && user.email) {
      targetRoute = '(tabs)';
    } else {
      targetRoute = '(auth)';
    }

    // Check if we're already on the correct route
    const isOnCorrectRoute = currentRoute === targetRoute;

    // Check if auth state has actually changed
    const authStateChanged = lastAuthState.current !== currentAuthState;

    // Decide whether to navigate
    const shouldNavigate =
      !initialNavigationDone.current || // Initial navigation
      (authStateChanged && !isOnCorrectRoute); // Auth changed and we're not on correct route

    if (!shouldNavigate) {
      console.log('⏭️ Skipping navigation:', {
        reason: isOnCorrectRoute ? 'already on correct route' : 'auth state unchanged',
        currentRoute,
        targetRoute
      });

      // Mark navigation as complete if we're stable
      if (!isNavigationComplete) {
        console.log('✅ Navigation marked as complete');
        setIsNavigationComplete(true);
      }
      return;
    }

    console.log('🧭 Navigating to:', targetRoute, {
      reason: !initialNavigationDone.current ? 'initial navigation' : 'auth state changed',
      from: currentRoute,
      to: targetRoute
    });

    // Update tracking variables
    lastAuthState.current = currentAuthState;
    initialNavigationDone.current = true;

    // Navigate with a small delay to ensure stability
    const timer = setTimeout(() => {
      router.replace(`/${targetRoute}` as any);
      setIsNavigationComplete(true);
    }, 50);

    return () => clearTimeout(timer);

  }, [user, loading, emailConfirmationRequired, rootNavigationState?.key, segments]);

  // Show loading until navigation is complete
  if (!isNavigationComplete) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}

export default function RootLayout() {
  useFrameworkReady();
  const [isReady, setIsReady] = useState(false);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
        retry: 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
      },
    },
  });

  useEffect(() => {
    // Small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationHandler>
          <MusicProvider>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="player" options={{ headerShown: false }} />
              <Stack.Screen name="album/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="light" />
          </MusicProvider>
        </NavigationHandler>
      </AuthProvider>
    </QueryClientProvider>
  );
}