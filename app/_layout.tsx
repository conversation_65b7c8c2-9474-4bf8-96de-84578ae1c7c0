import { useEffect, useState, useRef } from 'react';
import { router, Stack, useRootNavigationState, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { MusicProvider } from '@/contexts/MusicContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import './global.css';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, ActivityIndicator } from 'react-native';

// Loading component
function LoadingScreen() {
  return (
    <View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#0A0A0F'
    }}>
      <ActivityIndicator size="large" color="#A68752" />
    </View>
  );
}

// Navigation component that handles auth routing
function NavigationHandler({ children }: { children: React.ReactNode }) {
  const { user, loading, emailConfirmationRequired } = useAuth();
  const [isReady, setIsReady] = useState(false);
  const rootNavigationState = useRootNavigationState();
  const segments = useSegments();

  // Track navigation state
  const hasNavigated = useRef(false);
  const lastAuthState = useRef<string>('');

  useEffect(() => {
    // Wait for navigation system to be ready
    if (!rootNavigationState?.key) {
      console.log('⏳ Navigation system not ready');
      return;
    }

    // Wait for auth to finish loading
    if (loading) {
      console.log('⏳ Auth still loading');
      return;
    }

    // Create auth state signature
    const authState = `${!!user}-${emailConfirmationRequired}-${user?.email || 'none'}`;
    const currentRoute = segments[0];

    console.log('🔍 Navigation check:', {
      currentRoute,
      hasUser: !!user,
      userEmail: user?.email,
      emailConfirmed: !!user?.email_confirmed_at,
      emailConfirmationRequired,
      loading,
      hasNavigated: hasNavigated.current,
      authStateChanged: lastAuthState.current !== authState
    });

    // Determine target route
    let targetRoute: string;
    if (emailConfirmationRequired) {
      targetRoute = '(auth)';
    } else if (user && user.email) {
      targetRoute = '(tabs)';
    } else {
      targetRoute = '(auth)';
    }

    // Check if we need to navigate
    const isOnCorrectRoute = currentRoute === targetRoute;
    const authStateChanged = lastAuthState.current !== authState;
    const shouldNavigate = !hasNavigated.current || (authStateChanged && !isOnCorrectRoute);

    if (!shouldNavigate) {
      console.log('⏭️ Skipping navigation:', {
        reason: isOnCorrectRoute ? 'already on correct route' : 'auth unchanged',
        currentRoute,
        targetRoute
      });

      if (!isReady) {
        console.log('✅ Setting ready state');
        setIsReady(true);
      }
      return;
    }

    console.log('🧭 Navigating to:', targetRoute, {
      reason: !hasNavigated.current ? 'initial navigation' : 'auth changed',
      from: currentRoute,
      to: targetRoute
    });

    // Update state
    lastAuthState.current = authState;
    hasNavigated.current = true;

    // Navigate
    router.replace(`/${targetRoute}` as any);
    setIsReady(true);

  }, [user, loading, emailConfirmationRequired, rootNavigationState?.key, segments]);

  // Show loading until ready
  if (!isReady) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}

export default function RootLayout() {
  useFrameworkReady();
  const [isReady, setIsReady] = useState(false);

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 60 * 24,
        retry: 2,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
      },
    },
  });

  useEffect(() => {
    // Small delay to ensure everything is properly initialized
    const timer = setTimeout(() => {
      setIsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!isReady) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationHandler>
          <MusicProvider>
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="onboarding" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="player" options={{ headerShown: false }} />
              <Stack.Screen name="album/[id]" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
            <StatusBar style="light" />
          </MusicProvider>
        </NavigationHandler>
      </AuthProvider>
    </QueryClientProvider>
  );
}